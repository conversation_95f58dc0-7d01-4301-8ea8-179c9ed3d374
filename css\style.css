/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav-menu a:hover {
    background-color: rgba(255,255,255,0.2);
}

/* 主要内容区域 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 20px;
}

.hero {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    margin-bottom: 3rem;
    border-radius: 10px;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero p {
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto;
}

/* 卡片样式 */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card h3 {
    color: #667eea;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.card p {
    color: #666;
    margin-bottom: 1rem;
}

.card-link {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.7rem 1.5rem;
    text-decoration: none;
    border-radius: 5px;
    transition: opacity 0.3s;
}

.card-link:hover {
    opacity: 0.9;
}

/* 统计数据样式 */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #f5576c;
    display: block;
}

.stat-label {
    color: #666;
    margin-top: 0.5rem;
}

/* 图片样式 */
.image-container {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.image-container img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.image-container img:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 懒加载图片样式 */
.image-container img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.image-container img.lazy.loaded {
    opacity: 1;
}

/* 图片加载占位符 */
.image-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 10px;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 页脚样式 */
.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 4rem;
}

/* 移动端导航 */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav-menu.mobile-active {
        display: flex;
    }

    .nav-container {
        position: relative;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .card-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .container {
        padding: 1rem 15px;
    }

    .card {
        padding: 1.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    /* 修复生育现状页面的grid布局 */
    #survey-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
}

@media (max-width: 480px) {
    .stats {
        grid-template-columns: 1fr;
    }

    .hero h1 {
        font-size: 1.8rem;
    }

    .hero {
        padding: 2rem 0;
    }

    .stat-number {
        font-size: 2rem;
    }

    .card {
        padding: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .card-link {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .survey-content {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* 特殊样式 */
.highlight {
    background: linear-gradient(120deg, #f093fb 0%, #f5576c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin: 3rem 0 2rem 0;
    color: #333;
}

.divider {
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 2px;
    margin: 2rem auto;
    width: 100px;
}
